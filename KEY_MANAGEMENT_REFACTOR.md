# 密钥管理系统重构说明

## 重构概述

本次重构将密钥管理系统从基于 `channel + model` 的获取方式改为基于 `model` 名称获取一组密钥，并集成了智能调度算法来选择最优密钥。

## 主要变更

### 1. 核心架构变更

**重构前：**
- 基于 `KeyChannel + ModelName` 获取单个密钥
- 简单的轮询机制
- 每个模型只对应一个密钥

**重构后：**
- 基于 `ModelName` 获取一组可用密钥
- 使用 ε-Greedy 算法智能选择最优密钥
- 每个模型可以对应多个密钥，支持负载均衡和容错

### 2. 新增功能

#### 智能密钥选择
- **ε-Greedy 算法**：平衡探索和利用
- **动态评分**：基于权重和错误率计算密钥得分
- **错误率统计**：滑动窗口统计密钥使用成功率
- **自动过滤**：错误率过高的密钥自动被过滤

#### 密钥管理增强
- **权重调整**：支持动态调整密钥权重
- **临时冻结**：出现严重错误时临时冻结密钥
- **统计信息**：提供详细的密钥使用统计
- **渠道过滤**：支持指定特定渠道的密钥进行选择

## 核心类说明

### KeyManageService
重构后的核心密钥管理服务：

```kotlin
// 获取模型的最优密钥
fun findBestAvailableKey(modelName: String): AuthKey

// 获取模型的最优密钥（支持渠道过滤）
fun findBestAvailableKey(modelName: String, channels: List<KeyChannel>? = null): AuthKey

// 记录密钥使用结果
fun recordKeyUsageResult(modelName: String, keyId: Long, success: Boolean)

// 更新密钥权重
fun updateKeyWeight(keyId: Long, weight: Double)

// 临时冻结密钥
fun freezeKey(key: AuthKey, coldDownTime: Duration)
```

### KeyUsageService
新增的密钥使用服务，提供高级API：

```kotlin
// 自动选择最优密钥执行API调用
suspend fun <T> executeWithBestKey(
    modelName: String,
    apiCall: suspend (AuthKey) -> T
): T

// 自动选择最优密钥执行API调用（支持渠道过滤）
suspend fun <T> executeWithBestKey(
    modelName: String,
    channels: List<KeyChannel>? = null,
    apiCall: suspend (AuthKey) -> T
): T

// 批量执行API调用
suspend fun <T, R> executeBatch(
    modelName: String,
    requests: List<T>,
    apiCall: suspend (AuthKey, T) -> R
): List<R>

// 批量执行API调用（支持渠道过滤）
suspend fun <T, R> executeBatch(
    modelName: String,
    channels: List<KeyChannel>? = null,
    requests: List<T>,
    apiCall: suspend (AuthKey, T) -> R
): List<R>
```

### KeyScheduler
集成的调度器，实现智能密钥选择：

```kotlin
// 选择最优密钥
fun chooseKey(): String?

// 记录使用结果
fun recordResult(key: String, success: Boolean)

// 更新密钥权重
fun upsertKey(key: String, weight: Double)
```

## 配置说明

### 数据库字段配置
每个密钥现在都有独立的调度器配置，存储在数据库中：

```sql
-- 新增的调度器配置字段
ALTER TABLE common_code_key ADD COLUMN weight DECIMAL(5,2) NOT NULL DEFAULT 1.0;
ALTER TABLE common_code_key ADD COLUMN window_size INTEGER NOT NULL DEFAULT 100;
ALTER TABLE common_code_key ADD COLUMN epsilon DECIMAL(5,3) NOT NULL DEFAULT 0.05;
ALTER TABLE common_code_key ADD COLUMN err_threshold DECIMAL(5,3) NOT NULL DEFAULT 0.5;
```

### 前端配置界面
在创建/编辑密钥时，可以通过"高级选项"配置调度器参数：

- **权重 (Weight)**: 0.1-10.0，影响密钥被选中的概率
- **滑动窗口大小 (Window Size)**: 10-1000，统计错误率的样本数量
- **探索率 (Epsilon)**: 0.01-0.5，随机选择密钥的概率
- **错误阈值 (Error Threshold)**: 0.1-0.9，错误率超过此值的密钥将被过滤

### 全局默认配置
仍可在 `application-key-scheduler.yml` 中配置全局默认值：

```yaml
key-scheduler:
  default-window-size: 100      # 滑动窗口大小
  default-epsilon: 0.05         # 探索率
  default-err-threshold: 0.5    # 错误阈值

  model-configs:
    "claude-3-5-sonnet-20241022":
      window-size: 150
      epsilon: 0.03
      err-threshold: 0.3
```

### 参数说明
- **weight**: 密钥权重，权重越高被选中概率越大
- **window-size**: 滑动窗口大小，用于统计错误率
- **epsilon**: 探索率，控制随机选择的概率
- **err-threshold**: 错误阈值，超过此值的密钥将被过滤

## 渠道过滤功能

### 功能说明
渠道过滤功能允许在获取密钥时指定特定的渠道列表，系统将只从这些渠道的密钥中进行选择。这在以下场景中非常有用：

1. **API兼容性**：某些模型只在特定渠道可用
2. **成本控制**：优先使用成本较低的渠道
3. **性能优化**：选择响应速度更快的渠道
4. **合规要求**：某些业务场景只能使用特定渠道

### 支持的渠道
- `ClaudeCode`: Claude Code 渠道
- `Openai`: OpenAI 渠道
- `Azure`: Azure OpenAI 渠道
- `Agent`: Agent 渠道
- `Aws`: AWS Bedrock 渠道
- `Anthropic`: Anthropic 官方渠道
- `GoogleAiStudio`: Google AI Studio 渠道
- `GoogleVertexAI`: Google Vertex AI 渠道

### 使用方式
```kotlin
// 只从OpenAI渠道选择密钥
val key = keyManageService.findBestAvailableKey("gpt-4", listOf(KeyChannel.Openai))

// 从多个渠道中选择密钥
val key = keyManageService.findBestAvailableKey(
    "claude-3-5-sonnet-20241022",
    listOf(KeyChannel.ClaudeCode, KeyChannel.Anthropic)
)

// 不指定渠道（默认行为）
val key = keyManageService.findBestAvailableKey("gpt-4")
```

## 使用示例

### 基本使用
```kotlin
@Service
class MyApiService(
    private val keyUsageService: KeyUsageService
) {
    suspend fun callApi(modelName: String, request: String): String {
        return keyUsageService.executeWithBestKey(modelName) { key ->
            // 使用选中的密钥调用API
            apiClient.call(key.token, request)
        }
    }

    // 指定渠道过滤
    suspend fun callApiWithChannelFilter(modelName: String, request: String): String {
        return keyUsageService.executeWithBestKey(
            modelName,
            listOf(KeyChannel.Openai, KeyChannel.Anthropic)
        ) { key ->
            // 只从OpenAI和Anthropic渠道的密钥中选择
            apiClient.call(key.token, request)
        }
    }
}
```

### 批量调用
```kotlin
suspend fun batchCall(modelName: String, requests: List<String>): List<String> {
    return keyUsageService.executeBatch(modelName, requests) { key, request ->
        apiClient.call(key.token, request)
    }
}

// 批量调用（指定渠道过滤）
suspend fun batchCallWithChannelFilter(modelName: String, requests: List<String>): List<String> {
    return keyUsageService.executeBatch(
        modelName,
        listOf(KeyChannel.ClaudeCode),
        requests
    ) { key, request ->
        // 只从Claude Code渠道的密钥中选择
        apiClient.call(key.token, request)
    }
}
```

### 获取统计信息
```kotlin
val stats = keyUsageService.getModelKeyStats("claude-3-5-sonnet-20241022")
// 返回每个密钥的权重、错误率、得分等信息
```

## 兼容性

为了保持向后兼容，原有的API仍然可用：

```kotlin
// 旧API（已标记为废弃）
val key = keyManageService.findNextAvailableKey(channel, modelName)

// 新API（推荐使用）
val key = keyManageService.findBestAvailableKey(modelName)
```

## 监控和调试

### REST API
新增了REST API用于监控和调试：

```http
# 获取模型密钥统计
GET /api/v1/key-stats/model/{modelName}

# 更新密钥权重
POST /api/v1/key-stats/key/{keyId}/weight/{weight}

# 调整调度器配置
POST /api/v1/key-stats/model/{modelName}/config

# 手动记录密钥使用结果
POST /api/v1/key-stats/model/{modelName}/key/{keyId}/result
Content-Type: application/json
{
  "success": true
}

# 获取指定模型的最优密钥
GET /api/v1/key-stats/best-key/{modelName}?channels=CLAUDE_CODE,OPENAI
```

### 日志
系统提供详细的日志记录：
- 密钥选择过程
- 使用结果统计
- 错误率变化
- 配置更新

## 性能优化

1. **读写锁分离**：按模型维护独立的读写锁
2. **内存高效**：使用环形缓冲区统计错误率
3. **并发安全**：所有操作都是线程安全的
4. **智能缓存**：避免重复计算密钥得分

## 迁移指南

1. **数据库迁移**：执行 SQL 脚本添加新的调度器配置字段
   ```sql
   -- 执行迁移脚本
   -- src/main/resources/db/migration/V2__add_scheduler_config_fields.sql
   ```

2. **更新依赖**：确保项目包含 SchedulerConfig 相关类

3. **前端更新**：
   - 更新 TypeScript 类型定义
   - 集成 KeySchedulerConfigForm 组件到创建/编辑密钥的模态框
   - 在高级选项中提供调度器配置界面

4. **配置文件**：添加密钥调度器全局默认配置

5. **代码更新**：逐步替换旧API为新API

6. **测试验证**：
   - 验证密钥选择和统计功能
   - 测试前端配置界面
   - 验证数据库字段存储

7. **监控部署**：部署后监控密钥使用情况和调度器性能

## 故障排除

### 常见问题
1. **No available keys found**: 检查模型是否有配置密钥
2. **No available key from scheduler**: 检查错误阈值配置
3. **Key not found in model keys**: 检查密钥一致性

### 调试建议
1. 查看密钥统计信息
2. 调整调度器参数
3. 检查日志记录
4. 验证密钥配置
