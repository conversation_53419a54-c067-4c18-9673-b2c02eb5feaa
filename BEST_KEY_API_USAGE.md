# 最优密钥获取 API 使用说明

## 概述

新增的 `findBestAvailableKey` API 接口提供了一个外部调用方式来获取指定模型的最优密钥。该接口基于智能调度算法（ε-Greedy）来选择最佳的可用密钥。

## API 端点

### 获取最优密钥

**端点**: `GET /api/v1/key-stats/best-key/{modelName}`

**参数**:
- `modelName` (路径参数): 模型名称，如 `gpt-4`, `claude-3-5-sonnet-20241022`
- `channels` (查询参数，可选): 渠道列表，多个渠道用逗号分隔

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "token": "sk-xxx...",
    "channel": "Openai",
    "modelMapping": {
      "gpt-4": "gpt-4-turbo"
    },
    "name": "OpenAI Key 1",
    "weight": 1.5,
    "selectedAt": "2024-01-15T10:30:00"
  },
  "message": null,
  "code": 200
}
```

## 使用示例

### 1. 获取任意渠道的最优密钥

```bash
curl -X GET "http://localhost:8080/api/v1/key-stats/best-key/gpt-4"
```

### 2. 指定单个渠道

```bash
curl -X GET "http://localhost:8080/api/v1/key-stats/best-key/gpt-4?channels=Openai"
```

### 3. 指定多个渠道

```bash
curl -X GET "http://localhost:8080/api/v1/key-stats/best-key/claude-3-5-sonnet-20241022?channels=ClaudeCode,Anthropic"
```

### 4. JavaScript 调用示例

```javascript
// 获取 GPT-4 的最优密钥
async function getBestKey(modelName, channels = null) {
    const url = new URL(`/api/v1/key-stats/best-key/${modelName}`, 'http://localhost:8080');
    if (channels) {
        url.searchParams.set('channels', channels.join(','));
    }
    
    const response = await fetch(url);
    const result = await response.json();
    
    if (result.success) {
        return result.data;
    } else {
        throw new Error(result.message || 'Failed to get best key');
    }
}

// 使用示例
try {
    // 获取任意渠道的密钥
    const key1 = await getBestKey('gpt-4');
    console.log('Selected key:', key1);
    
    // 只从 OpenAI 渠道获取密钥
    const key2 = await getBestKey('gpt-4', ['Openai']);
    console.log('OpenAI key:', key2);
    
    // 从多个渠道中选择
    const key3 = await getBestKey('claude-3-5-sonnet-20241022', ['ClaudeCode', 'Anthropic']);
    console.log('Claude key:', key3);
} catch (error) {
    console.error('Error:', error.message);
}
```

### 5. Python 调用示例

```python
import requests
from typing import List, Optional, Dict, Any

def get_best_key(model_name: str, channels: Optional[List[str]] = None) -> Dict[str, Any]:
    """获取指定模型的最优密钥"""
    url = f"http://localhost:8080/api/v1/key-stats/best-key/{model_name}"
    params = {}
    
    if channels:
        params['channels'] = ','.join(channels)
    
    response = requests.get(url, params=params)
    result = response.json()
    
    if result['success']:
        return result['data']
    else:
        raise Exception(result.get('message', 'Failed to get best key'))

# 使用示例
try:
    # 获取任意渠道的密钥
    key1 = get_best_key('gpt-4')
    print(f"Selected key: {key1}")
    
    # 只从 OpenAI 渠道获取密钥
    key2 = get_best_key('gpt-4', ['Openai'])
    print(f"OpenAI key: {key2}")
    
    # 从多个渠道中选择
    key3 = get_best_key('claude-3-5-sonnet-20241022', ['ClaudeCode', 'Anthropic'])
    print(f"Claude key: {key3}")
except Exception as e:
    print(f"Error: {e}")
```

## 支持的渠道类型

- `ClaudeCode`: Claude Code 渠道
- `Openai`: OpenAI 渠道
- `Azure`: Azure OpenAI 渠道
- `Agent`: Agent 渠道
- `Aws`: AWS Bedrock 渠道
- `Anthropic`: Anthropic 官方渠道
- `GoogleAiStudio`: Google AI Studio 渠道
- `GoogleVertexAI`: Google Vertex AI 渠道

## 错误处理

### 常见错误情况

1. **模型不存在密钥**
```json
{
  "success": false,
  "data": null,
  "message": "No available keys found for model gpt-5",
  "code": 400
}
```

2. **指定渠道无可用密钥**
```json
{
  "success": false,
  "data": null,
  "message": "No available keys found for model gpt-4 with channels [Azure]",
  "code": 400
}
```

3. **无效的渠道名称**
- 无效的渠道名称会被自动忽略，只使用有效的渠道进行过滤

## 智能调度特性

该 API 使用了智能调度算法，具有以下特性：

1. **权重优先**: 权重高的密钥被选中的概率更大
2. **错误率过滤**: 错误率过高的密钥会被自动过滤
3. **探索与利用平衡**: 使用 ε-Greedy 算法平衡最优选择和随机探索
4. **实时统计**: 基于滑动窗口实时统计密钥使用效果

## 注意事项

1. **密钥加载**: 确保目标模型的密钥已经通过密钥管理系统加载
2. **渠道过滤**: 渠道参数区分大小写，请使用正确的渠道名称
3. **并发安全**: API 支持高并发调用，内部使用读写锁保证线程安全
4. **性能考虑**: 建议缓存获取到的密钥信息，避免频繁调用

## 与现有 API 的关系

这个新的 API 是对现有密钥管理系统的补充，提供了更直接的外部访问方式：

- **现有方式**: 通过 `KeyUsageService.executeWithBestKey()` 在代码中使用
- **新增方式**: 通过 REST API 直接获取密钥信息，适合外部系统集成

两种方式都使用相同的底层智能调度算法，保证了一致性。
