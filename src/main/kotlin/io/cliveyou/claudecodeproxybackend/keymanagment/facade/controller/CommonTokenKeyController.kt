package io.cliveyou.claudecodeproxybackend.keymanagment.facade.controller

import io.cliveyou.claudecodeproxybackend.common.response.PageResponse
import io.cliveyou.claudecodeproxybackend.common.response.PlatformResult
import io.cliveyou.claudecodeproxybackend.keymanagment.application.service.CommonTokenKeyService
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto.*
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.bind.annotation.*


@CrossOrigin()
@RestController
@RequestMapping("/api/v1/common-keys")
class CommonTokenKeyController(
    private val commonTokenKeyService: CommonTokenKeyService
) {
    
    private val log = KotlinLogging.logger {}

    @PostMapping
    fun createKey(@RequestBody request: CreateCommonTokenKeyRequest): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Creating ${request.type} key" }
        val response = commonTokenKeyService.createKey(request)
        return PlatformResult.success(response)
    }

    @PostMapping("/claude-code")
    fun createClaudeCodeKey(@RequestBody request: CreateClaudeCodeKeyRequest): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Creating Claude Code key" }
        val response = commonTokenKeyService.createKey(request)
        return PlatformResult.success(response)
    }

    @PostMapping("/google-vertex-ai/import")
    fun importGoogleVertexAIKey(@RequestBody request: ImportGoogleVertexAIKeyRequest): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Importing Google Vertex AI key from service account JSON with parameters" }
        val response = commonTokenKeyService.importGoogleVertexAIKey(request)
        return PlatformResult.success(response)
    }

    @PostMapping("/google-vertex-ai/import-simple")
    fun importGoogleVertexAIKeySimple(@RequestBody serviceAccountJson: String): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Importing Google Vertex AI key from service account JSON (simple)" }
        val response = commonTokenKeyService.importGoogleVertexAIKey(serviceAccountJson)
        return PlatformResult.success(response)
    }

    @PostMapping("/batch")
    fun createKeysBatch(@RequestBody request: BatchCreateCommonTokenKeyRequest): PlatformResult<BatchCreateResponse> {
        log.info { "Creating ${request.keys.size} keys in batch" }
        val response = commonTokenKeyService.createKeysBatch(request)
        return PlatformResult.success(response)
    }

    @PostMapping("/claude-code/batch")
    fun createClaudeCodeKeysBatch(@RequestBody request: BatchCreateClaudeCodeKeyRequest): PlatformResult<BatchCreateResponse> {
        log.info { "Creating ${request.keys.size} Claude Code keys in batch" }
        val response = commonTokenKeyService.createClaudeCodeKeysBatch(request)
        return PlatformResult.success(response)
    }

    @PutMapping("/{id}")
    fun updateKey(
        @PathVariable id: Long,
        @RequestBody request: UpdateCommonTokenKeyRequest
    ): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Updating key with ID: $id" }
        val requestWithId = when (request) {
            is UpdateClaudeCodeKeyRequest -> request.copy(id = id)
            else -> UpdateCommonTokenKeyRequest(
                id = id,
                accessToken = request.accessToken,
                domain = request.domain,
                status = request.status,
                name = request.name,
                supportModels = request.supportModels,
                autoDisable = request.autoDisable,
                modelMapping = request.modelMapping,
                quota = request.quota,
                weight = request.weight,
                windowSize = request.windowSize,
                epsilon = request.epsilon,
                errThreshold = request.errThreshold
            )
        }
        val response = commonTokenKeyService.updateKey(requestWithId)
        return PlatformResult.success(response)
    }

    @PutMapping("/claude-code/{id}")
    fun updateClaudeCodeKey(
        @PathVariable id: Long,
        @RequestBody request: UpdateClaudeCodeKeyRequest
    ): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Updating Claude Code key with ID: $id" }
        val response = commonTokenKeyService.updateKey(request.copy(id = id))
        return PlatformResult.success(response)
    }

    @DeleteMapping("/{id}")
    fun deleteKey(@PathVariable id: Long): PlatformResult<Void> {
        log.info { "Deleting key with ID: $id" }
        commonTokenKeyService.deleteKey(id)
        return PlatformResult.success()
    }

    @DeleteMapping("/batch")
    fun deleteKeys(@RequestBody ids: List<Long>): PlatformResult<Void> {
        log.info { "Batch deleting keys with IDs: $ids" }
        commonTokenKeyService.deleteKeys(ids)
        return PlatformResult.success()
    }

    @GetMapping("/{id}")
    fun getKey(@PathVariable id: Long): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Getting key with ID: $id" }
        val response = commonTokenKeyService.getKeyById(id)
        return PlatformResult.success(response)
    }

    @PostMapping("/page")
    fun pageKeys(@RequestBody request: PageCommonTokenKeyRequest): PlatformResult<PageResponse<CommonTokenKeyResponse>> {
        log.info { "Paging keys with request: $request" }
        val response = commonTokenKeyService.pageKeys(request)
        return PlatformResult.success(response)
    }

    @GetMapping("/active")
    fun getAllActiveKeys(): PlatformResult<List<CommonTokenKeyResponse>> {
        log.info { "Getting all active keys" }
        val response = commonTokenKeyService.getAllActiveKeys()
        return PlatformResult.success(response)
    }

    @GetMapping("/active/{type}")
    fun getActiveKeysByType(
        @PathVariable type: KeyChannel
    ): PlatformResult<List<CommonTokenKeyResponse>> {
        log.info { "Getting active keys for type: $type" }
        val response = commonTokenKeyService.getActiveKeysByType(type)
        return PlatformResult.success(response)
    }

    @PostMapping("/claude-code/{id}/flush")
    fun flushClaudeCodeToken(@PathVariable id: Long): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Flushing Claude Code token for key ID: $id" }
        val response = commonTokenKeyService.flushClaudeCodeToken(id)
        return PlatformResult.success(response)
    }
    
    @PutMapping("/google-vertex-ai/{id}")
    fun updateGoogleVertexAIKey(
        @PathVariable id: Long,
        @RequestBody request: UpdateGoogleVertexAIKeyRequest
    ): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Updating Google Vertex AI key with ID: $id" }
        val response = commonTokenKeyService.updateKey(request.copy(id = id))
        return PlatformResult.success(response)
    }

    @PostMapping("/google-vertex-ai/{id}/flush")
    fun flushGoogleVertexAIToken(@PathVariable id: Long): PlatformResult<CommonTokenKeyResponse> {
        log.info { "Flushing Google Vertex AI token for key ID: $id" }
        val response = commonTokenKeyService.flushGoogleVertexAIToken(id)
        return PlatformResult.success(response)
    }
}