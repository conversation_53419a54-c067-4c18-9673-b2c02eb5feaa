# 密钥监控可视化系统

## 概述

基于 `KeyManageService` 的数据创建了一个高密度、有主次显示的密钥监控可视化系统。该系统提供实时监控密钥性能、使用统计和调度器状态的功能。

## 系统架构

### 后端API接口

#### 1. 密钥统计控制器 (`KeyStatsController`)
新增了以下API接口：

- `GET /api/v1/key-stats/overview` - 获取密钥监控概览数据
- `GET /api/v1/key-stats/models/stats` - 获取所有模型的密钥统计信息  
- `GET /api/v1/key-stats/performance/trends` - 获取密钥性能趋势数据

#### 2. 数据传输对象 (DTOs)
- `KeyMonitoringOverview` - 密钥监控概览数据
- `ModelKeyStats` - 模型密钥统计信息
- `KeyStatInfo` - 密钥统计信息
- `KeyPerformanceTrend` - 密钥性能趋势数据
- `SchedulerConfigInfo` - 调度器配置信息

#### 3. 服务层增强 (`KeyUsageService`)
新增方法：
- `getKeyMonitoringOverview()` - 生成概览数据
- `getAllModelsKeyStats()` - 获取所有模型统计
- `getKeyPerformanceTrends()` - 获取性能趋势数据

### 前端组件系统

#### 1. 页面组件
- `src/app/dash/key-monitoring/page.tsx` - 主监控页面
- 集成了三个主要标签页：概览、模型统计、性能趋势

#### 2. 可视化组件
- `src/components/key-monitoring/key-overview-cards.tsx` - 概览卡片组件
- `src/components/key-monitoring/model-stats-table.tsx` - 模型统计表格组件
- `src/components/key-monitoring/key-performance-charts.tsx` - 性能图表组件

#### 3. API客户端
- `src/api/key-monitoring/key-monitoring-api.ts` - API客户端
- `src/api/key-monitoring/key-monitoring-types.ts` - TypeScript类型定义

## 功能特性

### 1. 概览仪表板
**主要指标卡片：**
- 总密钥数（活跃/禁用/认证失败）
- 平均成功率（带颜色编码和进度条）
- 总请求数（累计处理请求）
- 支持模型数量

**状态分布图表：**
- 密钥状态分布（活跃、禁用、认证失败）
- 渠道分布（各渠道密钥数量占比）

### 2. 模型统计详情
**可展开的模型卡片：**
- 模型基本信息（密钥数量、请求数、成功率）
- 调度器配置（探索率、错误阈值、窗口大小、最小样本）
- 密钥详情列表（权重、成功率、得分、请求数）
- 最佳/最差密钥标识

### 3. 性能趋势分析
**交互式图表：**
- 模型筛选器（全部模型或特定模型）
- 图表类型切换（成功率、请求量、错误率）
- 时间序列趋势图（面积图显示）
- 模型对比柱状图
- 实时状态指标

### 4. 数据密度优化
**高密度信息展示：**
- 紧凑的卡片布局
- 颜色编码的状态指示
- 百分比和数值的双重显示
- 响应式网格布局

**主次信息分层：**
- 主要指标突出显示（大字体、颜色强调）
- 次要信息以小字体和灰色显示
- 可展开的详细信息区域

## 技术实现

### 1. 实时数据更新
- 30秒轮询概览和模型统计数据
- 1分钟轮询性能趋势数据
- 手动刷新功能

### 2. 图表可视化
- 使用 Recharts 库
- 面积图显示趋势
- 柱状图显示对比
- 自定义颜色主题

### 3. 响应式设计
- 移动端友好布局
- 网格系统自适应
- 图表容器响应式

### 4. 错误处理
- 加载状态显示
- 空数据状态处理
- 错误信息提示

## 路由配置

已将密钥监控页面添加到路由系统：
- 路由路径：`/dashboard/key-monitoring`
- 侧边栏菜单：密钥监控（Activity图标）

## 数据流程

1. **数据收集**：`KeyScheduler` 实时收集密钥使用统计（请求数、成功数、错误率）
2. **数据聚合**：`KeyUsageService` 从调度器获取真实统计数据并聚合
3. **API暴露**：`KeyStatsController` 提供RESTful接口
4. **前端获取**：React组件通过API客户端获取数据
5. **可视化展示**：使用图表和卡片组件展示数据

## 真实数据源

### 统计数据来源
- **请求统计**：从 `KeyStats.totalRequests` 获取真实请求数量
- **成功统计**：从 `KeyStats.successRequests` 获取真实成功数量
- **错误率**：从 `KeyStats.errorRate` 获取实时错误率
- **调度器配置**：从 `KeyScheduler.getConfig()` 获取真实配置参数

### 数据特点
- **实时性**：数据直接来自运行中的调度器，反映当前真实状态
- **准确性**：基于滑动窗口的统计，避免了模拟数据的不准确性
- **动态性**：随着系统运行自动更新，无需手动维护

## 性能考虑

- 数据缓存：避免频繁计算统计数据
- 分页加载：大量数据时的性能优化
- 懒加载：按需加载图表组件
- 内存管理：及时清理定时器和订阅

## 扩展性

系统设计支持以下扩展：
- 新增监控指标
- 自定义图表类型
- 告警和通知功能
- 历史数据分析
- 导出功能

## 使用说明

1. 访问 `/dashboard/key-monitoring` 页面
2. 查看概览标签页了解整体状况
3. 切换到模型统计标签页查看详细信息
4. 使用性能趋势标签页分析历史数据
5. 利用筛选器和图表类型切换进行深入分析

该系统为密钥管理提供了全面的监控和可视化能力，帮助运维人员快速了解系统状态并做出决策。
