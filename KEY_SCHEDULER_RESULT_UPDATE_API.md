# KeyScheduler Result 更新接口文档

## 概述

本文档介绍如何使用新增的 API 接口来手动更新 KeyScheduler 的 result，用于调度器学习和优化密钥选择策略。

## API 接口

### 手动记录密钥使用结果

**接口地址：** `POST /api/v1/key-stats/model/{modelName}/key/{keyId}/result`

**功能描述：** 手动记录指定模型中某个密钥的使用结果，用于调度器学习和优化后续的密钥选择策略。

**路径参数：**
- `modelName` (String): 模型名称，如 "claude-3-5-sonnet-20241022"
- `keyId` (Long): 密钥ID

**请求体：**
```json
{
  "success": boolean
}
```

**请求体参数说明：**
- `success` (Boolean): 密钥使用是否成功
  - `true`: 表示密钥使用成功
  - `false`: 表示密钥使用失败

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

**错误响应：**
```json
{
  "code": 500,
  "message": "Failed to record key usage result: {错误详情}",
  "data": null
}
```

## 使用示例

### 1. 记录成功使用结果

```bash
curl -X POST "http://localhost:8080/api/v1/key-stats/model/claude-3-5-sonnet-20241022/key/123/result" \
  -H "Content-Type: application/json" \
  -d '{
    "success": true
  }'
```

### 2. 记录失败使用结果

```bash
curl -X POST "http://localhost:8080/api/v1/key-stats/model/claude-3-5-sonnet-20241022/key/123/result" \
  -H "Content-Type: application/json" \
  -d '{
    "success": false
  }'
```

### 3. JavaScript 示例

```javascript
// 记录成功结果
async function recordKeySuccess(modelName, keyId) {
  const response = await fetch(`/api/v1/key-stats/model/${modelName}/key/${keyId}/result`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      success: true
    })
  });
  
  const result = await response.json();
  console.log('记录结果:', result);
}

// 记录失败结果
async function recordKeyFailure(modelName, keyId) {
  const response = await fetch(`/api/v1/key-stats/model/${modelName}/key/${keyId}/result`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      success: false
    })
  });
  
  const result = await response.json();
  console.log('记录结果:', result);
}
```

### 4. Kotlin 代码示例

```kotlin
// 在服务层直接调用
@Service
class MyService(
    private val keyUsageService: KeyUsageService
) {
    
    fun recordManualResult(modelName: String, keyId: Long, success: Boolean) {
        keyUsageService.recordKeyUsageResult(modelName, keyId, success)
    }
}
```

## 工作原理

1. **接收请求**：API 接收模型名称、密钥ID和使用结果
2. **调用服务**：通过 `KeyManageService.recordKeyUsageResult()` 方法记录结果
3. **更新调度器**：调度器内部的 `KeyScheduler.recordResult()` 方法更新密钥统计信息
4. **影响选择**：后续的密钥选择会基于更新后的统计信息进行优化

## 调度器学习机制

### 错误率计算
- 使用滑动窗口统计最近 N 次调用的错误率
- 默认窗口大小为 100 次调用
- 错误率 = 失败次数 / 总调用次数

### 密钥得分计算
- 得分 = 权重 × (1 - 错误率)
- 权重可通过数据库配置或 API 动态调整
- 错误率通过实际使用结果动态更新

### 选择策略
- **ε-贪婪算法**：
  - 以 ε 概率随机选择（探索）
  - 以 (1-ε) 概率选择得分最高的密钥（利用）
- **错误率过滤**：错误率超过阈值的密钥会被暂时排除

## 相关接口

### 获取密钥统计信息
```bash
GET /api/v1/key-stats/model/{modelName}
```

### 获取最优密钥
```bash
GET /api/v1/key-stats/best-key/{modelName}?channels=CLAUDE_CODE,OPENAI
```

### 更新密钥权重
```bash
POST /api/v1/key-stats/key/{keyId}/weight/{weight}
```

### 调整调度器配置
```bash
POST /api/v1/key-stats/model/{modelName}/config
Content-Type: application/json
{
  "epsilon": 0.1,
  "errThreshold": 0.3,
  "windowSize": 200
}
```

## 注意事项

1. **模型名称**：必须是系统中已配置的有效模型名称
2. **密钥ID**：必须是该模型下已注册的有效密钥ID
3. **及时性**：建议在密钥使用后立即记录结果，以保证调度器学习的时效性
4. **准确性**：确保记录的成功/失败状态准确反映实际使用情况
5. **频率控制**：避免过于频繁的调用，建议批量处理或异步处理

## 日志记录

系统会记录详细的操作日志：

```
INFO  - Recording key usage result for model: claude-3-5-sonnet-20241022, keyId: 123, success: true
INFO  - Successfully recorded key usage result for model: claude-3-5-sonnet-20241022, keyId: 123, success: true
DEBUG - Record Result | Model: claude-3-5-sonnet-20241022, Key: 123, Success: true | Duration: 2ms
```

## 故障排除

### 常见错误

1. **模型不存在**
   - 错误信息：`No scheduler found for model {modelName}`
   - 解决方案：确认模型名称正确，或先加载该模型的密钥

2. **密钥不存在**
   - 错误信息：`Key {keyId} not found in model {modelName}`
   - 解决方案：确认密钥ID正确，或先将密钥添加到该模型

3. **请求格式错误**
   - 错误信息：`JSON parse error`
   - 解决方案：检查请求体格式是否正确

### 调试建议

1. 先调用获取统计信息接口确认模型和密钥状态
2. 检查日志中的详细错误信息
3. 确认网络连接和服务状态
